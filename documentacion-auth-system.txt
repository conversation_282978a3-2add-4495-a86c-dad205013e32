# Documentación del Sistema de Autenticación y Control de Acceso

## Introducción

Este documento describe la implementación del sistema de autenticación y control de acceso basado en roles (RBAC) para la aplicación. El sistema permite que diferentes tipos de usuarios (tienda, delivery, cliente normal, admin) accedan únicamente a las rutas y funcionalidades correspondientes a su rol.

## Componentes Principales

### 1. Contexto de Autenticación (AuthContext)

**Archivo**: `src/contexts/AuthContext.tsx`

**Propósito**: Proporcionar un estado global de autenticación accesible desde cualquier componente de la aplicación.

**Funcionalidades principales**:

- Almacenamiento del estado del usuario autenticado
- Verificación automática del token JWT al cargar la aplicación
- Normalización de tipos de cuenta para comparaciones consistentes
- Métodos para verificar roles de usuario
- Redirección automática para rutas protegidas
- Actualización de datos del usuario

**Métodos clave**:
- `checkUserRole(allowedRoles)`: Verifica si el usuario tiene alguno de los roles permitidos
- `refreshUserData()`: Actualiza los datos del usuario desde el servidor
- `logout()`: Cierra la sesión del usuario y limpia el estado

**Ejemplo de uso**:
```tsx
const { user, isAuthenticated, checkUserRole } = useAuth();

// Verificar si el usuario es un repartidor
const isDelivery = checkUserRole(['delivery']);

// Acceder a datos del usuario
const userName = user?.name;
```

### 2. Componente de Protección de Rutas (RouteGuard)

**Archivo**: `src/components/RouteGuard.tsx`

**Propósito**: Proteger rutas específicas para que solo sean accesibles por usuarios con roles autorizados.

**Funcionalidades principales**:
- Verificación de autenticación
- Verificación de roles permitidos
- Redirección a rutas alternativas para usuarios no autorizados
- Indicador de carga durante la verificación

**Props**:
- `children`: Componentes a renderizar si el usuario está autorizado
- `allowedRoles`: Array de roles que tienen permiso para acceder
- `redirectTo`: Ruta a la que redirigir si el usuario no está autorizado

**Ejemplo de uso**:
```tsx
<RouteGuard allowedRoles={['delivery']} redirectTo="/profile">
  <DeliveryDashboard />
</RouteGuard>
```

### 3. Layouts de Protección para Secciones Específicas

**Archivos**:
- `src/app/menu/delivery/layout.tsx` - Para rutas de repartidores
- `src/app/menu/store/layout.tsx` - Para rutas de tiendas
- `src/app/menu/catalog/layout.tsx` - Para rutas de clientes

**Propósito**: Aplicar protección a nivel de sección, asegurando que todas las páginas dentro de una sección específica estén protegidas con los mismos requisitos de rol.

**Ejemplo (Delivery Layout)**:
```tsx
export default function DeliveryLayout({ children }: DeliveryLayoutProps) {
  return (
    <RouteGuard allowedRoles={["delivery"]} redirectTo="/profile">
      {children}
    </RouteGuard>
  );
}
```

## Flujo de Autenticación

1. **Inicio de Sesión**:
   - El usuario ingresa credenciales en el formulario de login
   - Se envían al servidor mediante la función `login()` de `auth.ts`
   - Si son válidas, se recibe y almacena un token JWT en localStorage
   - Se cargan los datos del usuario y se actualizan en el contexto
   - Se redirige al usuario según su tipo de cuenta:
     - Tiendas → `/menu/store/dashboard`
     - Repartidores → `/menu/delivery/available`
     - Clientes → `/menu/catalog`

2. **Verificación de Acceso a Rutas**:
   - Cuando un usuario intenta acceder a una ruta protegida, el componente `RouteGuard` verifica:
     - Si el usuario está autenticado
     - Si tiene alguno de los roles permitidos
   - Si no cumple los requisitos, es redirigido a otra página (por defecto `/profile`)

3. **Cierre de Sesión**:
   - Se elimina el token JWT de localStorage
   - Se limpia el estado del usuario en el contexto
   - Se redirige al usuario a la página de login

## Normalización de Tipos de Cuenta

Para manejar diferentes nomenclaturas para los mismos roles, se implementó una función de normalización:

```tsx
const normalizeAccountType = (type: string): string => {
  type = type.toLowerCase();
  
  if (type === "tienda" || type === "store" || type === "seller") {
    return "tienda";
  } else if (type === "delivery" || type === "repartidor") {
    return "delivery";
  } else if (type === "admin" || type === "administrator") {
    return "admin";
  } else {
    return "normal"; // Cliente normal
  }
};
```

## Integración con la Interfaz de Usuario

### Barra de Navegación Dinámica

La barra de navegación (`src/components/Navbar.tsx`) se adapta según:
- Estado de autenticación: Muestra opciones de registro/login o perfil/logout
- Rol del usuario: Muestra enlaces específicos según el tipo de cuenta

### Perfil de Usuario Adaptativo

El componente de perfil (`src/app/profile/_ProfileForm.tsx`) muestra diferentes vistas según el tipo de cuenta:
- `StoreOwnerView`: Para tiendas, muestra información de la tienda
- `DeliveryUserView`: Para repartidores, muestra funciones de entrega
- `AdminUserView`: Para administradores, muestra funciones administrativas
- `NormalUserView`: Para clientes normales, muestra información básica

## Mejores Prácticas Implementadas

1. **Separación de Responsabilidades**:
   - Autenticación (login/logout) en servicios API
   - Estado global en contexto
   - Protección de rutas en componentes específicos

2. **Seguridad**:
   - Verificación de token en cada solicitud API
   - Protección de rutas en el cliente
   - Normalización de roles para evitar bypass

3. **Experiencia de Usuario**:
   - Indicadores de carga durante verificaciones
   - Redirección inteligente según el rol
   - Interfaz adaptativa según el tipo de usuario

## Extensibilidad

El sistema está diseñado para ser fácilmente extensible:

1. **Nuevos Roles**: Simplemente añadir nuevos casos en la función `normalizeAccountType`
2. **Nuevas Rutas Protegidas**: Envolver con el componente `RouteGuard` o crear un nuevo layout
3. **Permisos Granulares**: Extender `checkUserRole` para verificar permisos específicos además de roles

## Conclusión

Esta implementación proporciona un sistema robusto de autenticación y control de acceso basado en roles, asegurando que los usuarios solo puedan acceder a las funcionalidades correspondientes a su tipo de cuenta. La arquitectura modular facilita el mantenimiento y la extensión del sistema según las necesidades futuras de la aplicación.
