'use client';

import React from 'react';
import StoreLayout from '@/components/layouts/StoreLayout';
import DashboardSummary from '@/components/store/DashboardSummary';
import RecentOrders from '@/components/store/RecentOrders';
import { useDashboard, useOrders } from '@/hooks/useStore';

export default function StoreDashboardPage() {
  const { stats, loading: statsLoading, error: statsError } = useDashboard();
  const { orders, loading: ordersLoading, error: ordersError } = useOrders();

  // Obtener los pedidos más recientes (últimos 4)
  const recentOrders = orders.slice(0, 4).map(order => ({
    id: order._id,
    customer: order.customer.name,
    date: order.date,
    total: order.total,
    status: order.status === 'ready' ? 'processing' : order.status, // Mapear 'ready' a 'processing'
    items: order.items.map(item => ({
      name: item.name,
      quantity: item.quantity,
    })),
  }));

  if (statsLoading || ordersLoading) {
    return (
      <StoreLayout>
        <div className="min-h-screen bg-[#F7F3E9] flex items-center justify-center">
          <div className="text-[#D05A44]">Cargando dashboard...</div>
        </div>
      </StoreLayout>
    );
  }

  if (statsError || ordersError || !stats) {
    return (
      <StoreLayout>
        <div className="min-h-screen bg-[#F7F3E9] flex items-center justify-center">
          <div className="text-red-600">
            Error: {statsError || ordersError || 'No se pudieron cargar las estadísticas'}
          </div>
        </div>
      </StoreLayout>
    );
  }

  return (
    <StoreLayout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Dashboard de Tienda</h1>
        <p className="text-gray-600">Resumen del rendimiento de tu tienda</p>
      </div>

      <div className="mb-8">
        <DashboardSummary stats={stats} />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div>
          <RecentOrders orders={recentOrders} />
        </div>
        <div>
          <div className="bg-white p-6 rounded-lg border shadow-sm h-full">
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <div className="space-y-4">
              <a
                href="/menu/store/products"
                className="block p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center">
                  <div className="text-2xl mr-4">📦</div>
                  <div>
                    <h3 className="font-medium">Manage Products</h3>
                    <p className="text-sm text-gray-500">Add, edit or remove products from your catalog</p>
                  </div>
                </div>
              </a>
              <a
                href="/menu/store/orders"
                className="block p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center">
                  <div className="text-2xl mr-4">📋</div>
                  <div>
                    <h3 className="font-medium">View All Orders</h3>
                    <p className="text-sm text-gray-500">See all orders and their statuses</p>
                  </div>
                </div>
              </a>
              <a
                href="/menu/store/suppliers"
                className="block p-4 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex items-center">
                  <div className="text-2xl mr-4">🚚</div>
                  <div>
                    <h3 className="font-medium">Manage Suppliers</h3>
                    <p className="text-sm text-gray-500">Add or edit your supplier relationships</p>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </StoreLayout>
  );
}
