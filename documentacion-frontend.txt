# Documentación del Frontend - Plataforma Comunitaria de Comercio Local

## Introducción

Este documento explica la estructura del frontend desarrollado para la plataforma comunitaria de comercio local. El frontend está construido con React 19 y Next.js, siguiendo un enfoque de componentes reutilizables y páginas específicas para cada rol de usuario.

## Estructura General

El proyecto está organizado de la siguiente manera:

- `src/app`: Contiene las páginas de la aplicación (estructura de Next.js)
- `src/components`: Contiene los componentes reutilizables
  - `catalog`: Componentes para la visualización de productos
  - `delivery`: Componentes para el rol de repartidor
  - `layouts`: Componentes de diseño para cada tipo de usuario
  - `store`: Componentes para el rol de tienda/locatario
  - `ui`: Componentes básicos de UI (botones, inputs, etc.)

## Roles de Usuario

La aplicación contempla tres tipos de usuarios:

1. **Cliente (Comprador)**
2. **Tienda (Locatario)**
3. **Repartidor**

Cada rol tiene su propio conjunto de páginas y componentes específicos.

## Componentes por Rol

### 1. Cliente (Comprador)

#### Páginas:
- **Catálogo** (`/menu/catalog`): Muestra todos los productos disponibles con filtros.
- **Detalle de Producto** (`/menu/catalog/[productId]`): Muestra información detallada de un producto y permite comprarlo.

#### Componentes Principales:
- `ProductCard`: Tarjeta que muestra información básica de un producto en el catálogo.
- `FilterSidebar`: Barra lateral con filtros para el catálogo (precio, preferencias dietéticas, calorías).
- `ProductGrid`: Cuadrícula que organiza las tarjetas de productos.
- `ProductDetail`: Muestra información detallada de un producto.
- `PurchaseOptions`: Opciones para comprar un producto (cantidad, método de entrega).
- `ReviewSection`: Sección para ver y dejar reseñas.

#### Datos Esperados del Backend:

Para el catálogo:
```typescript
interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  store: string;
  tags: string[];
}
```

Para el detalle del producto:
```typescript
interface ProductDetail {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  store: {
    id: string;
    name: string;
    rating: number;
  };
  tags: string[];
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
  availableOptions: {
    delivery: boolean;
    pickup: boolean;
  };
}
```

Para las reseñas:
```typescript
interface Review {
  id: string;
  user: string;
  rating: number;
  comment: string;
  date: string;
}
```

### 2. Tienda (Locatario)

#### Páginas:
- **Dashboard** (`/menu/store/dashboard`): Resumen general de la tienda.
- **Productos** (`/menu/store/products`): Gestión de productos.
- **Pedidos** (`/menu/store/orders`): Gestión de pedidos recibidos.
- **Proveedores** (`/menu/store/suppliers`): Gestión de proveedores.
- **Reseñas** (`/menu/store/reviews`): Visualización de reseñas recibidas.

#### Componentes Principales:
- `DashboardSummary`: Resumen de estadísticas de la tienda.
- `RecentOrders`: Lista de pedidos recientes.
- `ProductsList`: Lista de productos de la tienda.
- `ProductForm`: Formulario para crear/editar productos.
- `OrdersList`: Lista de pedidos.
- `OrderDetail`: Detalle de un pedido específico.
- `SuppliersList`: Lista de proveedores.
- `SupplierForm`: Formulario para crear/editar proveedores.
- `ReviewsList`: Lista de reseñas recibidas.

#### Datos Esperados del Backend:

Para el dashboard:
```typescript
interface DashboardStats {
  totalProducts: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  averageRating: number;
}
```

Para los productos:
```typescript
interface StoreProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  stock: number;
  category: string;
  image: string;
  tags: string[];
  deliveryOptions: {
    delivery: boolean;
    pickup: boolean;
  };
  nutritionalInfo?: {
    calories: number;
    protein: number;
    carbs: number;
    fat: number;
  };
}
```

Para los pedidos:
```typescript
interface Order {
  id: string;
  customer: {
    name: string;
    address?: string;
    phone: string;
    email?: string;
  };
  date: string;
  total: number;
  status: 'pending' | 'processing' | 'ready' | 'delivered' | 'cancelled';
  deliveryMethod: 'delivery' | 'pickup';
  items: {
    id: string;
    name: string;
    quantity: number;
    price: number;
  }[];
  notes?: string;
}
```

Para los proveedores:
```typescript
interface Supplier {
  id: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  products: string[];
}
```

Para las reseñas:
```typescript
interface StoreReview {
  id: string;
  customer: string;
  rating: number;
  comment: string;
  date: string;
  productId?: string;
  productName?: string;
}

interface ReviewsData {
  reviews: StoreReview[];
  averageRating: number;
  totalReviews: number;
}
```

### 3. Repartidor

#### Páginas:
- **Pedidos Disponibles** (`/menu/delivery/available`): Muestra pedidos disponibles para entregar.
- **Mis Pedidos** (`/menu/delivery/my-orders`): Muestra pedidos asignados al repartidor.

#### Componentes Principales:
- `AvailableOrdersList`: Lista de pedidos disponibles para entregar.
- `OrderCard`: Tarjeta que muestra información de un pedido.
- `MyOrdersList`: Lista de pedidos asignados al repartidor.
- `OrderStatusUpdate`: Componente para actualizar el estado de un pedido.

#### Datos Esperados del Backend:

Para los pedidos disponibles:
```typescript
interface AvailableOrder {
  id: string;
  store: {
    name: string;
    address: string;
  };
  customer: {
    address: string;
    distance: number; // en km
  };
  items: number; // cantidad de items
  total: number;
  estimatedTime: number; // en minutos
}
```

Para los pedidos asignados:
```typescript
interface DeliveryOrder {
  id: string;
  store: {
    name: string;
    address: string;
    phone: string;
  };
  customer: {
    name: string;
    address: string;
    phone: string;
  };
  items: {
    name: string;
    quantity: number;
  }[];
  status: 'accepted' | 'picked_up' | 'on_way' | 'delivered';
  acceptedAt: string;
  estimatedDelivery: string;
}
```

## Endpoints API Necesarios

Para implementar el backend, se necesitarán los siguientes endpoints:

### Cliente (Comprador)

- `GET /api/products`: Obtener lista de productos con filtros opcionales
- `GET /api/products/:id`: Obtener detalles de un producto específico
- `GET /api/stores/:id/reviews`: Obtener reseñas de una tienda
- `POST /api/reviews`: Crear una nueva reseña
- `POST /api/orders`: Crear un nuevo pedido

### Tienda (Locatario)

- `GET /api/store/dashboard`: Obtener estadísticas del dashboard
- `GET /api/store/products`: Obtener productos de la tienda
- `POST /api/store/products`: Crear un nuevo producto
- `PUT /api/store/products/:id`: Actualizar un producto existente
- `DELETE /api/store/products/:id`: Eliminar un producto
- `GET /api/store/orders`: Obtener pedidos de la tienda
- `GET /api/store/orders/:id`: Obtener detalles de un pedido específico
- `PUT /api/store/orders/:id/status`: Actualizar el estado de un pedido
- `GET /api/store/suppliers`: Obtener proveedores de la tienda
- `POST /api/store/suppliers`: Crear un nuevo proveedor
- `PUT /api/store/suppliers/:id`: Actualizar un proveedor existente
- `DELETE /api/store/suppliers/:id`: Eliminar un proveedor
- `GET /api/store/reviews`: Obtener reseñas de la tienda

### Repartidor

- `GET /api/delivery/available`: Obtener pedidos disponibles para entregar
- `GET /api/delivery/orders`: Obtener pedidos asignados al repartidor
- `POST /api/delivery/orders/:id/accept`: Aceptar un pedido
- `PUT /api/delivery/orders/:id/status`: Actualizar el estado de un pedido

## Modelos de Datos Sugeridos

Para implementar el backend, se sugieren los siguientes modelos de datos:

### Usuario
```
- id: string
- name: string
- email: string
- password: string (hash)
- phone: string
- address: string
- role: 'client' | 'store' | 'delivery'
- storeData?: {
    storeName: string
    storeAddress: string
    storePhone: string
  }
```

### Producto
```
- id: string
- storeId: string
- name: string
- description: string
- price: number
- stock: number
- category: string
- image: string
- tags: string[]
- deliveryOptions: {
    delivery: boolean
    pickup: boolean
  }
- nutritionalInfo?: {
    calories: number
    protein: number
    carbs: number
    fat: number
  }
- createdAt: Date
- updatedAt: Date
```

### Pedido
```
- id: string
- clientId: string
- storeId: string
- deliveryId?: string
- items: {
    productId: string
    name: string
    quantity: number
    price: number
  }[]
- total: number
- status: 'pending' | 'processing' | 'ready' | 'on_way' | 'delivered' | 'cancelled'
- deliveryMethod: 'delivery' | 'pickup'
- notes?: string
- createdAt: Date
- updatedAt: Date
```

### Reseña
```
- id: string
- clientId: string
- storeId: string
- productId?: string
- rating: number
- comment: string
- createdAt: Date
```

### Proveedor
```
- id: string
- storeId: string
- name: string
- contactPerson: string
- email: string
- phone: string
- address: string
- products: string[]
- createdAt: Date
- updatedAt: Date
```

## Consideraciones para el Backend

1. **Autenticación y Autorización**: Implementar JWT para autenticación y verificar roles para autorización.
2. **Validación de Datos**: Validar todos los datos recibidos del frontend.
3. **Manejo de Imágenes**: Implementar almacenamiento para imágenes de productos.
4. **Notificaciones**: Considerar implementar notificaciones en tiempo real para pedidos nuevos y cambios de estado.
5. **Geolocalización**: Para el rol de repartidor, considerar implementar servicios de geolocalización.

## Conclusión

Este frontend proporciona una base sólida para la plataforma comunitaria de comercio local. Al implementar el backend correspondiente siguiendo las estructuras de datos y endpoints sugeridos, se logrará una aplicación completa y funcional que satisfaga las necesidades de los tres roles de usuario.
