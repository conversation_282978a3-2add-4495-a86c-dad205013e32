# Documentación de Integración del Microservicio de Tiendas

## Resumen

Se ha implementado la integración completa entre el frontend y el microservicio de tiendas, conectando todas las funcionalidades descritas en `documentacion-micro-Store.txt` con las páginas y componentes del frontend.

## Archivos Creados/Modificados

### 1. Servicios de API

**Archivo**: `src/services/api/store.ts`
- Contiene todas las funciones para comunicarse con el microservicio de tiendas
- Incluye interfaces TypeScript para todos los tipos de datos
- Maneja autenticación con JWT
- Endpoints implementados:
  - Dashboard: `getDashboardStats()`
  - Productos: `getProducts()`, `createProduct()`, `updateProduct()`, `deleteProduct()`
  - Pedidos: `getOrders()`, `getOrderById()`, `updateOrderStatus()`
  - Proveedores: `getSuppliers()`, `createSupplier()`, `updateSupplier()`, `deleteSupplier()`
  - Reseñas: `getReviews()`

### 2. Hooks Personalizados

**Archivo**: `src/hooks/useStore.ts`
- Hooks para manejar el estado de los datos del microservicio
- Incluye manejo de loading, errores y operaciones CRUD
- Hooks implementados:
  - `useDashboard()`: Para estadísticas del dashboard
  - `useProducts()`: Para gestión de productos
  - `useOrders()`: Para gestión de pedidos
  - `useSuppliers()`: Para gestión de proveedores
  - `useReviews()`: Para visualización de reseñas

### 3. Páginas Actualizadas

#### Dashboard (`src/app/menu/store/dashboard/page.tsx`)
- Conectado con `useDashboard()` y `useOrders()`
- Muestra estadísticas reales del microservicio
- Muestra pedidos recientes con datos reales
- Manejo de estados de carga y error

#### Productos (`src/app/menu/store/products/page.tsx`)
- Conectado con `useProducts()`
- CRUD completo de productos
- Mapeo de datos entre interfaces del microservicio y componentes UI
- Manejo de formularios para crear/editar productos

#### Pedidos (`src/app/menu/store/orders/page.tsx`)
- Conectado con `useOrders()`
- Visualización y gestión de pedidos
- Actualización de estados de pedidos
- Vista detallada de pedidos individuales

#### Proveedores (`src/app/menu/store/suppliers/page.tsx`)
- Conectado con `useSuppliers()`
- CRUD completo de proveedores
- Formularios para gestión de proveedores

#### Reseñas (`src/app/menu/store/reviews/page.tsx`)
- Conectado con `useReviews()`
- Visualización de reseñas de clientes
- Estadísticas de calificaciones

## Configuración del Microservicio

### URL Base
```typescript
const STORE_BASE_URL = 'http://localhost:3001/api';
```

### Autenticación
- Utiliza JWT tokens almacenados en localStorage
- Headers de autorización automáticos en todas las requests
- Manejo de tokens expirados con redirección a login

### Manejo de Errores
- Captura de errores HTTP
- Mensajes de error específicos
- Redirección automática en caso de autenticación fallida

## Interfaces TypeScript

### Principales Interfaces Implementadas

```typescript
interface DashboardStats {
  totalProducts: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  averageRating: number;
}

interface Product {
  _id?: string;
  name: string;
  description: string;
  price: number;
  stock: number;
  category: string;
  image: string;
  tags: string[];
  deliveryOptions: {
    delivery: boolean;
    pickup: boolean;
  };
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };
}

interface Order {
  _id: string;
  customer: {
    name: string;
    address?: string;
    phone: string;
    email?: string;
  };
  date: string;
  total: number;
  status: 'pending' | 'processing' | 'ready' | 'delivered' | 'cancelled';
  deliveryMethod: 'delivery' | 'pickup';
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
  }>;
  notes?: string;
}

interface Supplier {
  _id?: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  products: string[];
}

interface Review {
  _id: string;
  customer: string;
  rating: number;
  comment: string;
  date: string;
  productId: string;
  productName: string;
}
```

## Mapeo de Datos

### Problema Resuelto
Los componentes UI existentes esperaban interfaces diferentes a las del microservicio (principalmente `id` vs `_id`). Se implementó mapeo de datos en cada página para convertir entre formatos.

### Ejemplo de Mapeo
```typescript
const mappedProducts = products.map(product => ({
  id: product._id || '',
  name: product.name,
  price: product.price,
  stock: product.stock,
  category: product.category,
  image: product.image || '/images/products/default.jpg',
}));
```

## Funcionalidades Implementadas

### Dashboard
- ✅ Estadísticas en tiempo real
- ✅ Pedidos recientes
- ✅ Indicadores de rendimiento

### Gestión de Productos
- ✅ Listar productos
- ✅ Crear nuevo producto
- ✅ Editar producto existente
- ✅ Eliminar producto
- ✅ Información nutricional
- ✅ Opciones de entrega

### Gestión de Pedidos
- ✅ Listar pedidos
- ✅ Filtrar por estado
- ✅ Ver detalles de pedido
- ✅ Actualizar estado de pedido
- ✅ Información de cliente y entrega

### Gestión de Proveedores
- ✅ Listar proveedores
- ✅ Crear nuevo proveedor
- ✅ Editar proveedor existente
- ✅ Eliminar proveedor
- ✅ Gestión de productos por proveedor

### Reseñas de Clientes
- ✅ Visualizar reseñas
- ✅ Estadísticas de calificaciones
- ✅ Filtrado por producto

## Próximos Pasos

1. **Testing**: Implementar tests unitarios para los hooks y servicios
2. **Optimización**: Implementar cache y optimistic updates
3. **Validación**: Añadir validación de formularios más robusta
4. **Notificaciones**: Implementar sistema de notificaciones en tiempo real
5. **Paginación**: Añadir paginación para listas grandes
6. **Filtros**: Implementar filtros avanzados en todas las listas

## Notas Técnicas

- Todos los endpoints están configurados para el puerto 3001 del microservicio
- Se mantiene compatibilidad con los componentes UI existentes
- El sistema de autenticación está integrado con el contexto global
- Los errores se manejan de forma consistente en toda la aplicación
- Los datos se mapean automáticamente entre formatos del microservicio y UI
