# Prompt para Microservicio de Store - Especificaciones CRUD

## Descripción General
Este documento especifica los endpoints y estructuras de datos que debe implementar el microservicio de Store para funcionar correctamente con el frontend de la aplicación de delivery.

## Base URL
```
http://localhost:3001/api/store
```

## Autenticación
Todos los endpoints requieren autenticación Bearer Token en el header:
```
Authorization: Bearer <token>
```

---

## 1. DASHBOARD - Estadísticas de la Tienda

### Endpoint: GET /dashboard
**Descripción**: Obtiene estadísticas generales de la tienda

**Respuesta esperada**:
```json
{
  "totalProducts": 25,
  "pendingOrders": 8,
  "completedOrders": 142,
  "totalRevenue": 15420.50,
  "averageRating": 4.3
}
```

**Campos requeridos**:
- `totalProducts` (number): Total de productos activos
- `pendingOrders` (number): Pedidos pendientes de procesar
- `completedOrders` (number): Pedidos completados
- `totalRevenue` (number): Ingresos totales
- `averageRating` (number): Calificación promedio (0-5)

---

## 2. PRODUCTOS - Gestión de Catálogo

### GET /products
**Descripción**: Obtiene todos los productos de la tienda

**Respuesta esperada**:
```json
[
  {
    "_id": "64a1b2c3d4e5f6789012345",
    "name": "Empanadas de Pino",
    "description": "Deliciosas empanadas chilenas rellenas de carne, cebolla y aceitunas",
    "price": 2500,
    "stock": 50,
    "category": "Comida Tradicional",
    "image": "https://example.com/empanadas.jpg",
    "tags": ["tradicional", "carne", "chilena"],
    "deliveryOptions": {
      "delivery": true,
      "pickup": true
    },
    "nutritionalInfo": {
      "calories": 320,
      "protein": 15,
      "carbs": 25,
      "fat": 18
    }
  }
]
```

### POST /products
**Descripción**: Crea un nuevo producto

**Body esperado**:
```json
{
  "name": "Nombre del producto",
  "description": "Descripción detallada",
  "price": 2500,
  "stock": 50,
  "category": "Categoría",
  "image": "URL de la imagen",
  "tags": ["tag1", "tag2"],
  "deliveryOptions": {
    "delivery": true,
    "pickup": true
  },
  "nutritionalInfo": {
    "calories": 320,
    "protein": 15,
    "carbs": 25,
    "fat": 18
  }
}
```

### PATCH /products/:id
**Descripción**: Actualiza un producto existente

**Body**: Campos a actualizar (parcial del objeto producto)

### DELETE /products/:id
**Descripción**: Elimina un producto

---

## 3. PEDIDOS - Gestión de Orders

### GET /orders
**Descripción**: Obtiene todos los pedidos
**Query params opcionales**: `?status=pending` para filtrar por estado

**Respuesta esperada**:
```json
[
  {
    "_id": "64a1b2c3d4e5f6789012346",
    "customer": {
      "name": "María González",
      "address": "Av. Providencia 1234, Santiago",
      "phone": "+56912345678",
      "email": "<EMAIL>"
    },
    "date": "2024-01-15T10:30:00Z",
    "total": 7500,
    "status": "pending",
    "deliveryMethod": "delivery",
    "items": [
      {
        "id": "64a1b2c3d4e5f6789012345",
        "name": "Empanadas de Pino",
        "quantity": 3,
        "price": 2500
      }
    ],
    "notes": "Sin aceitunas por favor"
  }
]
```

### GET /orders/:id
**Descripción**: Obtiene un pedido específico

### PATCH /orders/:id/status
**Descripción**: Actualiza el estado de un pedido

**Body esperado**:
```json
{
  "status": "processing"
}
```

**Estados válidos**: "pending", "processing", "ready", "delivered", "cancelled"

---

## 4. PROVEEDORES - Gestión de Suppliers

### GET /suppliers
**Descripción**: Obtiene todos los proveedores

**Respuesta esperada**:
```json
[
  {
    "_id": "64a1b2c3d4e5f6789012347",
    "name": "Distribuidora San Miguel",
    "contactPerson": "Carlos Rodríguez",
    "email": "<EMAIL>",
    "phone": "+56987654321",
    "address": "Calle Los Aromos 567, Maipú",
    "products": ["Carnes", "Verduras", "Lácteos"]
  }
]
```

### POST /suppliers
**Descripción**: Crea un nuevo proveedor

**Body esperado**:
```json
{
  "name": "Nombre del proveedor",
  "contactPerson": "Persona de contacto",
  "email": "<EMAIL>",
  "phone": "+56912345678",
  "address": "Dirección completa",
  "products": ["Producto1", "Producto2"]
}
```

### PATCH /suppliers/:id
**Descripción**: Actualiza un proveedor

### DELETE /suppliers/:id
**Descripción**: Elimina un proveedor

---

## 5. RESEÑAS - Gestión de Reviews

### GET /reviews
**Descripción**: Obtiene todas las reseñas de la tienda

**Respuesta esperada**:
```json
{
  "reviews": [
    {
      "_id": "64a1b2c3d4e5f6789012348",
      "customer": "Ana Pérez",
      "rating": 5,
      "comment": "Excelente comida, muy sabrosa y llegó caliente",
      "date": "2024-01-14T15:45:00Z",
      "productId": "64a1b2c3d4e5f6789012345",
      "productName": "Empanadas de Pino"
    }
  ],
  "averageRating": 4.3,
  "totalReviews": 87
}
```

---

## 6. MANEJO DE ERRORES

### Códigos de Estado HTTP
- `200`: Éxito
- `201`: Creado exitosamente
- `400`: Error en los datos enviados
- `401`: No autorizado
- `404`: Recurso no encontrado
- `500`: Error interno del servidor

### Formato de Error
```json
{
  "error": true,
  "message": "Descripción del error",
  "code": "ERROR_CODE"
}
```

---

## 7. DATOS DE EJEMPLO CHILENOS

### Productos Sugeridos:
- Empanadas de Pino
- Pastel de Choclo
- Cazuela de Cordero
- Completos Italianos
- Sopaipillas
- Mote con Huesillo
- Churrasco Palta
- Humitas

### Categorías:
- Comida Tradicional
- Comida Rápida
- Bebidas
- Postres
- Vegetariano

### Nombres Chilenos:
- María González, Carlos Rodríguez, Ana Pérez, Luis Morales, Carmen Silva

### Direcciones Chilenas:
- Av. Providencia 1234, Santiago
- Calle Los Aromos 567, Maipú
- Pasaje Las Flores 89, Ñuñoa

---

## 8. NOTAS IMPORTANTES

1. **Todos los precios están en pesos chilenos (CLP)**
2. **Las fechas deben estar en formato ISO 8601**
3. **Los IDs deben ser ObjectId de MongoDB**
4. **Implementar validación de datos en todos los endpoints**
5. **Manejar casos donde no hay datos (devolver arrays vacíos, no errores)**
6. **Implementar paginación para listas grandes**
7. **Logs detallados para debugging**

---

## 9. ENDPOINTS ADICIONALES OPCIONALES

### GET /analytics/sales
**Descripción**: Datos para gráficos de ventas

### GET /inventory/low-stock
**Descripción**: Productos con stock bajo

### POST /products/bulk-update
**Descripción**: Actualización masiva de productos

---

## 10. EJEMPLOS DE IMPLEMENTACIÓN

### Estructura de Base de Datos (MongoDB)

#### Colección: products
```javascript
{
  _id: ObjectId,
  name: String,
  description: String,
  price: Number,
  stock: Number,
  category: String,
  image: String,
  tags: [String],
  deliveryOptions: {
    delivery: Boolean,
    pickup: Boolean
  },
  nutritionalInfo: {
    calories: Number,
    protein: Number,
    carbs: Number,
    fat: Number
  },
  storeId: ObjectId, // ID de la tienda
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Colección: orders
```javascript
{
  _id: ObjectId,
  storeId: ObjectId,
  customer: {
    name: String,
    address: String,
    phone: String,
    email: String
  },
  items: [{
    productId: ObjectId,
    name: String,
    quantity: Number,
    price: Number
  }],
  total: Number,
  status: String, // pending, processing, ready, delivered, cancelled
  deliveryMethod: String, // delivery, pickup
  notes: String,
  createdAt: Date,
  updatedAt: Date
}
```

### Ejemplo de Controller (Node.js/Express)

```javascript
// controllers/dashboardController.js
exports.getDashboardStats = async (req, res) => {
  try {
    const storeId = req.user.storeId; // Del token JWT

    const [
      totalProducts,
      pendingOrders,
      completedOrders,
      revenueData,
      avgRating
    ] = await Promise.all([
      Product.countDocuments({ storeId, isActive: true }),
      Order.countDocuments({ storeId, status: 'pending' }),
      Order.countDocuments({ storeId, status: 'delivered' }),
      Order.aggregate([
        { $match: { storeId, status: 'delivered' } },
        { $group: { _id: null, total: { $sum: '$total' } } }
      ]),
      Review.aggregate([
        { $match: { storeId } },
        { $group: { _id: null, avg: { $avg: '$rating' } } }
      ])
    ]);

    res.json({
      totalProducts,
      pendingOrders,
      completedOrders,
      totalRevenue: revenueData[0]?.total || 0,
      averageRating: avgRating[0]?.avg || 0
    });
  } catch (error) {
    res.status(500).json({ error: true, message: error.message });
  }
};
```

### Middleware de Autenticación

```javascript
// middleware/auth.js
const jwt = require('jsonwebtoken');

exports.authenticateStore = async (req, res, next) => {
  try {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: true, message: 'Token requerido' });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Verificar que el usuario sea de tipo 'Store'
    if (decoded.accountType !== 'Store') {
      return res.status(403).json({ error: true, message: 'Acceso denegado' });
    }

    req.user = decoded;
    next();
  } catch (error) {
    res.status(401).json({ error: true, message: 'Token inválido' });
  }
};
```

---

## 11. VALIDACIONES REQUERIDAS

### Validación de Productos
```javascript
const productValidation = {
  name: { required: true, minLength: 2, maxLength: 100 },
  description: { required: true, minLength: 10, maxLength: 500 },
  price: { required: true, min: 0, type: 'number' },
  stock: { required: true, min: 0, type: 'number' },
  category: { required: true, minLength: 2, maxLength: 50 },
  image: { required: true, format: 'url' },
  tags: { type: 'array', maxItems: 10 }
};
```

### Validación de Pedidos
```javascript
const orderValidation = {
  'customer.name': { required: true, minLength: 2, maxLength: 100 },
  'customer.phone': { required: true, pattern: /^\+56[0-9]{9}$/ },
  'customer.address': { required: true, minLength: 10, maxLength: 200 },
  items: { required: true, minItems: 1, maxItems: 50 },
  deliveryMethod: { required: true, enum: ['delivery', 'pickup'] }
};
```

---

## 12. CASOS ESPECIALES A MANEJAR

### 1. Tienda Nueva (Sin Datos)
- Dashboard debe devolver todos los valores en 0
- Listas deben devolver arrays vacíos
- No mostrar errores, sino estados vacíos

### 2. Productos Agotados
- Mantener en la lista pero marcar stock: 0
- Permitir edición para reponer stock

### 3. Pedidos Cancelados
- Mantener historial pero marcar como cancelled
- No incluir en cálculos de revenue

### 4. Manejo de Imágenes
- Validar URLs de imágenes
- Tener imagen por defecto si falla la carga
- Considerar subida de archivos local

---

## 13. TESTING RECOMENDADO

### Datos de Prueba
```javascript
// Crear productos de prueba
const testProducts = [
  {
    name: "Empanadas de Pino",
    description: "Tradicionales empanadas chilenas con carne, cebolla y aceitunas",
    price: 2500,
    stock: 30,
    category: "Comida Tradicional",
    image: "https://example.com/empanadas.jpg",
    tags: ["tradicional", "carne", "chilena"]
  },
  // ... más productos
];
```

### Casos de Prueba Importantes
1. CRUD completo para cada entidad
2. Filtros y búsquedas
3. Validaciones de datos
4. Manejo de errores
5. Autenticación y autorización
6. Performance con datos grandes
