import { useState, useEffect } from 'react';
import {
  getDashboardStats,
  getProducts,
  createProduct,
  updateProduct,
  deleteProduct,
  getOrders,
  updateOrderStatus,
  getSuppliers,
  createSupplier,
  updateSupplier,
  deleteSupplier,
  getReviews,
  type DashboardStats,
  type Product,
  type Order,
  type Supplier,
  type ReviewsResponse,
} from '@/services/api/store';

// Hook para el dashboard
export const useDashboard = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const data = await getDashboardStats();
      setStats(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar estadísticas');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  };
};

// Hook para productos
export const useProducts = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const data = await getProducts();
      setProducts(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar productos');
    } finally {
      setLoading(false);
    }
  };

  const addProduct = async (productData: Omit<Product, '_id'>) => {
    try {
      const newProduct = await createProduct(productData);
      setProducts(prev => [newProduct, ...prev]);
      return newProduct;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error al crear producto';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const editProduct = async (id: string, productData: Partial<Product>) => {
    try {
      const updatedProduct = await updateProduct(id, productData);
      setProducts(prev =>
        prev.map(p => p._id === id ? updatedProduct : p)
      );
      return updatedProduct;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error al actualizar producto';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const removeProduct = async (id: string) => {
    try {
      await deleteProduct(id);
      setProducts(prev => prev.filter(p => p._id !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error al eliminar producto';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    fetchProducts();
  }, []);

  return {
    products,
    loading,
    error,
    addProduct,
    editProduct,
    removeProduct,
    refetch: fetchProducts,
  };
};

// Hook para pedidos
export const useOrders = (initialStatus?: string) => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchOrders = async (status?: string) => {
    try {
      setLoading(true);
      const data = await getOrders(status);
      setOrders(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar pedidos');
    } finally {
      setLoading(false);
    }
  };

  const changeOrderStatus = async (id: string, status: Order['status']) => {
    try {
      const updatedOrder = await updateOrderStatus(id, status);
      setOrders(prev =>
        prev.map(order => order._id === id ? updatedOrder : order)
      );
      return updatedOrder;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error al actualizar estado del pedido';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    fetchOrders(initialStatus);
  }, [initialStatus]);

  return {
    orders,
    loading,
    error,
    changeOrderStatus,
    refetch: (status?: string) => fetchOrders(status),
  };
};

// Hook para proveedores
export const useSuppliers = () => {
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSuppliers = async () => {
    try {
      setLoading(true);
      const data = await getSuppliers();
      setSuppliers(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar proveedores');
    } finally {
      setLoading(false);
    }
  };

  const addSupplier = async (supplierData: Omit<Supplier, '_id'>) => {
    try {
      const newSupplier = await createSupplier(supplierData);
      setSuppliers(prev => [newSupplier, ...prev]);
      return newSupplier;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error al crear proveedor';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const editSupplier = async (id: string, supplierData: Partial<Supplier>) => {
    try {
      const updatedSupplier = await updateSupplier(id, supplierData);
      setSuppliers(prev =>
        prev.map(s => s._id === id ? updatedSupplier : s)
      );
      return updatedSupplier;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error al actualizar proveedor';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const removeSupplier = async (id: string) => {
    try {
      await deleteSupplier(id);
      setSuppliers(prev => prev.filter(s => s._id !== id));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error al eliminar proveedor';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  useEffect(() => {
    fetchSuppliers();
  }, []);

  return {
    suppliers,
    loading,
    error,
    addSupplier,
    editSupplier,
    removeSupplier,
    refetch: fetchSuppliers,
  };
};

// Hook para reseñas
export const useReviews = () => {
  const [reviewsData, setReviewsData] = useState<ReviewsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReviews = async () => {
    try {
      setLoading(true);
      const data = await getReviews();
      setReviewsData(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar reseñas');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, []);

  return {
    reviews: reviewsData?.reviews || [],
    averageRating: reviewsData?.averageRating || 0,
    totalReviews: reviewsData?.totalReviews || 0,
    loading,
    error,
    refetch: fetchReviews,
  };
};
