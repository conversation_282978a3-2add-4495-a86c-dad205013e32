import { apiRequest } from './base';

// Base URL para el microservicio de tiendas
const STORE_BASE_URL = 'http://localhost:3001/api';

// Interfaces para TypeScript
export interface DashboardStats {
  totalProducts: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  averageRating: number;
}

export interface Product {
  _id?: string;
  name: string;
  description: string;
  price: number;
  stock: number;
  category: string;
  image: string;
  tags: string[];
  deliveryOptions: {
    delivery: boolean;
    pickup: boolean;
  };
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
  };
}

export interface Order {
  _id: string;
  customer: {
    name: string;
    address?: string;
    phone: string;
    email?: string;
  };
  date: string;
  total: number;
  status: 'pending' | 'processing' | 'ready' | 'delivered' | 'cancelled';
  deliveryMethod: 'delivery' | 'pickup';
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
  }>;
  notes?: string;
}

export interface Supplier {
  _id?: string;
  name: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  products: string[];
}

export interface Review {
  _id: string;
  customer: string;
  rating: number;
  comment: string;
  date: string;
  productId: string;
  productName: string;
}

export interface ReviewsResponse {
  reviews: Review[];
  averageRating: number;
  totalReviews: number;
}

// Función helper para hacer requests al microservicio de tiendas
const storeRequest = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('token');
  
  const config: RequestInit = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers,
    },
  };

  const response = await fetch(`${STORE_BASE_URL}${endpoint}`, config);
  
  if (!response.ok) {
    if (response.status === 401) {
      // Token expirado o inválido
      localStorage.removeItem('token');
      window.location.href = '/login';
      throw new Error('Sesión expirada');
    }
    
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.message || `Error ${response.status}: ${response.statusText}`);
  }

  return response.json();
};

// ===== DASHBOARD =====
export const getDashboardStats = async (): Promise<DashboardStats> => {
  return storeRequest('/store/dashboard');
};

// ===== PRODUCTOS =====
export const getProducts = async (): Promise<Product[]> => {
  return storeRequest('/store/products');
};

export const createProduct = async (productData: Omit<Product, '_id'>): Promise<Product> => {
  return storeRequest('/store/products', {
    method: 'POST',
    body: JSON.stringify(productData),
  });
};

export const updateProduct = async (id: string, productData: Partial<Product>): Promise<Product> => {
  return storeRequest(`/store/products/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(productData),
  });
};

export const deleteProduct = async (id: string): Promise<void> => {
  return storeRequest(`/store/products/${id}`, {
    method: 'DELETE',
  });
};

// ===== PEDIDOS =====
export const getOrders = async (status?: string): Promise<Order[]> => {
  const query = status ? `?status=${status}` : '';
  return storeRequest(`/store/orders${query}`);
};

export const getOrderById = async (id: string): Promise<Order> => {
  return storeRequest(`/store/orders/${id}`);
};

export const updateOrderStatus = async (id: string, status: Order['status']): Promise<Order> => {
  return storeRequest(`/store/orders/${id}/status`, {
    method: 'PATCH',
    body: JSON.stringify({ status }),
  });
};

// ===== PROVEEDORES =====
export const getSuppliers = async (): Promise<Supplier[]> => {
  return storeRequest('/store/suppliers');
};

export const createSupplier = async (supplierData: Omit<Supplier, '_id'>): Promise<Supplier> => {
  return storeRequest('/store/suppliers', {
    method: 'POST',
    body: JSON.stringify(supplierData),
  });
};

export const updateSupplier = async (id: string, supplierData: Partial<Supplier>): Promise<Supplier> => {
  return storeRequest(`/store/suppliers/${id}`, {
    method: 'PATCH',
    body: JSON.stringify(supplierData),
  });
};

export const deleteSupplier = async (id: string): Promise<void> => {
  return storeRequest(`/store/suppliers/${id}`, {
    method: 'DELETE',
  });
};

// ===== RESEÑAS =====
export const getReviews = async (): Promise<ReviewsResponse> => {
  return storeRequest('/store/reviews');
};
